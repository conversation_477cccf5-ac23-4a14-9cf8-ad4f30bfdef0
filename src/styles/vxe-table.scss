// 自定义 Vxe Table 样式

.vxe-grid {
  // 表单
  &--form-wrapper {
    .vxe-form {
      padding: 10px 20px;
      margin-bottom: 20px;
    }
  }

  // 工具栏
  &--toolbar-wrapper {
    .vxe-toolbar {
      padding: 20px;
    }
  }

  // 分页
  &--pager-wrapper {
    .vxe-pager {
      height: 70px;
      padding: 0 20px;

      &--wrapper {
        // 参考 Bootstrap 的响应式设计 WIDTH = 768
        @media screen and (width <= 768px) {
          .vxe-pager--total,
          .vxe-pager--sizes,
          .vxe-pager--jump,
          .vxe-pager--jump-prev,
          .vxe-pager--jump-next {
            display: none;
          }
        }
      }
    }
  }
}
