/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module "vue" {
  export interface GlobalComponents {
    AppLink: (typeof import("./../components/AppLink/index.vue"))["default"];
    Breadcrumb: (typeof import("./../components/Breadcrumb/index.vue"))["default"];
    CopyButton: (typeof import("./../components/CopyButton/index.vue"))["default"];
    CURD: (typeof import("./../components/CURD/index.vue"))["default"];
    Dict: (typeof import("./../components/Dict/index.vue"))["default"];
    DictLabel: (typeof import("./../components/Dict/DictLabel.vue"))["default"];
    ECharts: (typeof import("./../components/ECharts/index.vue"))["default"];
    ElBacktop: (typeof import("element-plus/es"))["ElBacktop"];
    ElBreadcrumb: (typeof import("element-plus/es"))["ElBreadcrumb"];
    ElBreadcrumbItem: (typeof import("element-plus/es"))["ElBreadcrumbItem"];
    ElButton: (typeof import("element-plus/es"))["ElButton"];
    ElCard: (typeof import("element-plus/es"))["ElCard"];
    ElCascader: (typeof import("element-plus/es"))["ElCascader"];
    ElCheckbox: (typeof import("element-plus/es"))["ElCheckbox"];
    ElCheckboxGroup: (typeof import("element-plus/es"))["ElCheckboxGroup"];
    ElCol: (typeof import("element-plus/es"))["ElCol"];
    ElColorPicker: (typeof import("element-plus/es"))["ElColorPicker"];
    ElConfigProvider: (typeof import("element-plus/es"))["ElConfigProvider"];
    ElDatePicker: (typeof import("element-plus/es"))["ElDatePicker"];
    ElDialog: (typeof import("element-plus/es"))["ElDialog"];
    ElDivider: (typeof import("element-plus/es"))["ElDivider"];
    ElDrawer: (typeof import("element-plus/es"))["ElDrawer"];
    ElDropdown: (typeof import("element-plus/es"))["ElDropdown"];
    ElDropdownItem: (typeof import("element-plus/es"))["ElDropdownItem"];
    ElDropdownMenu: (typeof import("element-plus/es"))["ElDropdownMenu"];
    ElForm: (typeof import("element-plus/es"))["ElForm"];
    ElFormItem: (typeof import("element-plus/es"))["ElFormItem"];
    ElIcon: (typeof import("element-plus/es"))["ElIcon"];
    ElImage: (typeof import("element-plus/es"))["ElImage"];
    ElInput: (typeof import("element-plus/es"))["ElInput"];
    ElInputTag: (typeof import("element-plus/es"))["ElInputTag"];
    ElInputNumber: (typeof import("element-plus/es"))["ElInputNumber"];
    ElLink: (typeof import("element-plus/es"))["ElLink"];
    ElMenu: (typeof import("element-plus/es"))["ElMenu"];
    ElMenuItem: (typeof import("element-plus/es"))["ElMenuItem"];
    ElOption: (typeof import("element-plus/es"))["ElOption"];
    ElPagination: (typeof import("element-plus/es"))["ElPagination"];
    ElPopover: (typeof import("element-plus/es"))["ElPopover"];
    ElRadio: (typeof import("element-plus/es"))["ElRadio"];
    ElRadioGroup: (typeof import("element-plus/es"))["ElRadioGroup"];
    ElRow: (typeof import("element-plus/es"))["ElRow"];
    ElScrollbar: (typeof import("element-plus/es"))["ElScrollbar"];
    ElSelect: (typeof import("element-plus/es"))["ElSelect"];
    ElStatistic: (typeof import("element-plus/es"))["ElStatistic"];
    ElSubMenu: (typeof import("element-plus/es"))["ElSubMenu"];
    ElSwitch: (typeof import("element-plus/es"))["ElSwitch"];
    ElTable: (typeof import("element-plus/es"))["ElTable"];
    ElTableColumn: (typeof import("element-plus/es"))["ElTableColumn"];
    ElTag: (typeof import("element-plus/es"))["ElTag"];
    ElText: (typeof import("element-plus/es"))["ElText"];
    ElTimeSelect: (typeof import("element-plus/es"))["ElTimeSelect"];
    ElTooltip: (typeof import("element-plus/es"))["ElTooltip"];
    ElTree: (typeof import("element-plus/es"))["ElTree"];
    ElTreeSelect: (typeof import("element-plus/es"))["ElTreeSelect"];
    ElUpload: (typeof import("element-plus/es"))["ElUpload"];
    ElWatermark: (typeof import("element-plus/es"))["ElWatermark"];
    ElSkeleton: (typeof import("element-plus/es"))["ElSkeleton"];
    FileUpload: (typeof import("./../components/Upload/FileUpload.vue"))["default"];
    Form: (typeof import("./../components/CURD/Form.vue"))["default"];
    Fullscreen: (typeof import("./../components/Fullscreen/index.vue"))["default"];
    GithubCorner: (typeof import("./../components/GithubCorner/index.vue"))["default"];
    Hamburger: (typeof import("./../components/Hamburger/index.vue"))["default"];
    IconSelect: (typeof import("./../components/IconSelect/index.vue"))["default"];
    LangSelect: (typeof import("./../components/LangSelect/index.vue"))["default"];
    MenuSearch: (typeof import("./../components/MenuSearch/index.vue"))["default"];
    MultiImageUpload: (typeof import("./../components/Upload/MultiImageUpload.vue"))["default"];
    Notification: (typeof import("./../components/Notification/index.vue"))["default"];
    PageContent: (typeof import("./../components/CURD/PageContent.vue"))["default"];
    PageModal: (typeof import("./../components/CURD/PageModal.vue"))["default"];
    PageSearch: (typeof import("./../components/CURD/PageSearch.vue"))["default"];
    Pagination: (typeof import("./../components/Pagination/index.vue"))["default"];
    RouterLink: (typeof import("vue-router"))["RouterLink"];
    RouterView: (typeof import("vue-router"))["RouterView"];
    SingleImageUpload: (typeof import("./../components/Upload/SingleImageUpload.vue"))["default"];
    SizeSelect: (typeof import("./../components/SizeSelect/index.vue"))["default"];
    TableSelect: (typeof import("./../components/TableSelect/index.vue"))["default"];
    WangEditor: (typeof import("./../components/WangEditor/index.vue"))["default"];
  }
  export interface ComponentCustomProperties {
    vLoading: (typeof import("element-plus/es"))["ElLoadingDirective"];
  }
}
