<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <!-- Agent数量 -->
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card stat-card-blue">
          <div class="stat-icon">
            <el-icon size="24"><Avatar /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ agentStats.total }}</div>
            <div class="stat-label">Agent数量</div>
          </div>
        </div>
      </el-col>

      <!-- 已启用Agent -->
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card stat-card-green">
          <div class="stat-icon">
            <el-icon size="24"><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ agentStats.enabled }}</div>
            <div class="stat-label">已启用Agent</div>
          </div>
        </div>
      </el-col>

      <!-- 今日调用 -->
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card stat-card-orange">
          <div class="stat-icon">
            <el-icon size="24"><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(usageStats.todayCalls) }}</div>
            <div class="stat-label">今日调用</div>
          </div>
        </div>
      </el-col>

      <!-- 成功率 -->
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card stat-card-cyan">
          <div class="stat-icon">
            <el-icon size="24"><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ usageStats.successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="mt-5">
      <!-- 系统状态 -->
      <el-col :xs="24" :md="8">
        <el-card shadow="never" class="h-full">
          <template #header>
            <div class="flex items-center">
              <el-icon class="mr-2"><Monitor /></el-icon>
              <span>系统状态</span>
            </div>
          </template>
          <div class="system-status">
            <div v-for="item in systemStatus" :key="item.name" class="status-item">
              <div class="status-info">
                <span class="status-name">{{ item.name }}</span>
                <el-tag :type="item.status === 'online' ? 'success' : 'danger'" size="small">
                  {{ item.status === "online" ? "在线" : "离线" }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最近Agent -->
      <el-col :xs="24" :md="8">
        <el-card shadow="never" class="h-full">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <el-icon class="mr-2"><User /></el-icon>
                <span>最近Agent</span>
              </div>
            </div>
          </template>
          <div class="recent-agents">
            <div v-for="agent in recentAgents" :key="agent.id" class="agent-item">
              <div class="agent-info">
                <div class="agent-name">{{ agent.name }}</div>
                <div class="agent-id">{{ agent.id }}</div>
              </div>
              <el-tag :type="agent.status === 'active' ? 'success' : 'warning'" size="small">
                {{ agent.status === "active" ? "活跃" : "待机" }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 中继操作 -->
      <el-col :xs="24" :md="8">
        <el-card shadow="never" class="h-full">
          <template #header>
            <div class="flex items-center">
              <el-icon class="mr-2"><Setting /></el-icon>
              <span>中继操作</span>
            </div>
          </template>
          <div class="relay-operations">
            <el-button type="primary" class="w-full mb-3" @click="createAgent">
              <el-icon class="mr-2"><Plus /></el-icon>
              创建新Agent
            </el-button>
            <div class="operation-links">
              <el-link type="primary" class="block mb-2" @click="manageAgents">
                <el-icon class="mr-1"><Setting /></el-icon>
                管理中继设置
              </el-link>
              <el-link type="primary" class="block" @click="viewLogs">
                <el-icon class="mr-1"><Document /></el-icon>
                查看调用日志
              </el-link>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 使用统计图表 -->
    <el-row :gutter="20" class="mt-5">
      <el-col :span="24">
        <el-card shadow="never">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <el-icon class="mr-2"><TrendCharts /></el-icon>
                <span>使用统计</span>
              </div>
              <div class="chart-controls">
                <span class="mr-3 text-sm text-gray-500">今日概况</span>
                <div class="usage-summary">
                  <span class="summary-item">
                    <span class="summary-label">API调用</span>
                    <span class="summary-value">{{ formatNumber(usageStats.todayCalls) }}</span>
                  </span>
                  <span class="summary-item ml-4">
                    <span class="summary-label">平均响应</span>
                    <span class="summary-value">{{ usageStats.avgResponse }}ms</span>
                  </span>
                  <span class="summary-item ml-4">
                    <span class="summary-label">错误率</span>
                    <span class="summary-value">{{ usageStats.errorRate }}%</span>
                  </span>
                </div>
              </div>
            </div>
          </template>
          <ECharts :options="usageChartOptions" height="300px" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Dashboard",
  inheritAttrs: false,
});

import {
  Avatar,
  Check,
  TrendCharts,
  CircleCheck,
  Monitor,
  User,
  Setting,
  Plus,
  Document,
} from "@element-plus/icons-vue";

// Agent统计数据
const agentStats = ref({
  total: 9,
  enabled: 0,
});

// 使用统计数据
const usageStats = ref({
  todayCalls: 4253,
  successRate: 95.7,
  avgResponse: 245,
  errorRate: 4.3,
});

// 系统状态数据
const systemStatus = ref([
  { name: "database", status: "online" },
  { name: "fileStorage", status: "online" },
  { name: "redis", status: "online" },
  { name: "limService", status: "online" },
]);

// 最近Agent数据
const recentAgents = ref([
  {
    id: "Agent_001",
    name: "主要助理new_Agent",
    status: "active",
  },
]);

// 格式化数字显示
const formatNumber = (num: number): string => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "k";
  }
  return num.toString();
};

// 使用统计图表配置
const usageChartOptions = ref({
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "line",
    },
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    data: ["00:00", "04:00", "08:00", "12:00", "16:00", "20:00", "24:00"],
    axisLine: {
      lineStyle: {
        color: "#e0e6ed",
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#8c9cac",
    },
  },
  yAxis: {
    type: "value",
    splitLine: {
      lineStyle: {
        color: "#f0f3f7",
        type: "dashed",
      },
    },
    axisLabel: {
      color: "#8c9cac",
    },
  },
  series: [
    {
      name: "API调用",
      type: "line",
      smooth: true,
      data: [120, 200, 150, 800, 700, 1100, 1234],
      lineStyle: {
        color: "#4080ff",
        width: 3,
      },
      itemStyle: {
        color: "#4080ff",
      },
      areaStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: "rgba(64, 128, 255, 0.3)" },
            { offset: 1, color: "rgba(64, 128, 255, 0.05)" },
          ],
        },
      },
    },
    {
      name: "平均响应时间",
      type: "line",
      smooth: true,
      data: [220, 180, 191, 234, 290, 330, 245],
      lineStyle: {
        color: "#36cfc9",
        width: 2,
      },
      itemStyle: {
        color: "#36cfc9",
      },
    },
  ],
});

// 事件处理方法
const createAgent = () => {
  console.log("创建新Agent");
  // 这里可以跳转到创建Agent页面或打开弹窗
};

const manageAgents = () => {
  console.log("管理中继设置");
  // 这里可以跳转到Agent管理页面
};

const viewLogs = () => {
  console.log("查看调用日志");
  // 这里可以跳转到日志页面
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 50px);

  .stats-cards {
    margin-bottom: 20px;
  }

  .stat-card {
    display: flex;
    align-items: center;
    padding: 24px;
    border-radius: 12px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    &.stat-card-blue {
      background: linear-gradient(135deg, #4080ff 0%, #3366cc 100%);
    }

    &.stat-card-green {
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    }

    &.stat-card-orange {
      background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
    }

    &.stat-card-cyan {
      background: linear-gradient(135deg, #13c2c2 0%, #08979c 100%);
    }

    .stat-icon {
      margin-right: 16px;
      opacity: 0.8;
    }

    .stat-content {
      flex: 1;

      .stat-number {
        font-size: 28px;
        font-weight: bold;
        line-height: 1;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        opacity: 0.9;
      }
    }
  }

  .system-status {
    .status-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px solid var(--el-border-color-lighter);

      &:last-child {
        border-bottom: none;
      }

      .status-name {
        font-size: 14px;
        color: var(--el-text-color-primary);
      }
    }
  }

  .recent-agents {
    .agent-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px solid var(--el-border-color-lighter);

      &:last-child {
        border-bottom: none;
      }

      .agent-info {
        flex: 1;

        .agent-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }

        .agent-id {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .relay-operations {
    .operation-links {
      .el-link {
        display: block;
        margin-bottom: 8px;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .chart-controls {
    display: flex;
    align-items: center;

    .usage-summary {
      display: flex;
      align-items: center;

      .summary-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .summary-label {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-bottom: 2px;
        }

        .summary-value {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;

    .stat-card {
      padding: 16px;
      margin-bottom: 12px;

      .stat-content .stat-number {
        font-size: 24px;
      }
    }

    .chart-controls {
      flex-direction: column;
      align-items: flex-start;

      .usage-summary {
        margin-top: 8px;
        flex-wrap: wrap;

        .summary-item {
          margin-right: 16px;
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>
