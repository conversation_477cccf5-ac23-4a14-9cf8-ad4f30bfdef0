<template>
  <div class="logo">
    <transition enter-active-class="animate__animated animate__fadeInLeft">
      <router-link :key="+collapse" class="wh-full flex-center" to="/">
        <div class="logo-icon">
          <img :src="logo" alt="logo" />
        </div>
        <span v-if="!collapse" class="title">
          {{ defaultSettings.title }}
        </span>
      </router-link>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { defaultSettings } from "@/settings";
import logo from "@/assets/robot.svg";

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});
</script>

<style lang="scss" scoped>
.logo {
  width: 100%;
  height: $navbar-height;
  background-color: $menu-background;

  .logo-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 20px;
      height: 20px;
      // 默认主题下的深色图标（原始颜色）
      filter: none;
    }
  }

  .title {
    flex-shrink: 0;
    margin-left: 10px;
    font-size: 14px;
    font-weight: bold;
    color: $sidebar-logo-text-color;
  }
}
</style>

<style lang="scss">
// 顶部布局和混合布局的特殊处理
.layout-top,
.layout-mix {
  .logo {
    background-color: transparent !important;

    .logo-icon {
      img {
        // 根据主题动态调整图标颜色
        filter: none;
      }
    }

    .title {
      color: var(--menu-text);
    }
  }
}

// 顶部布局和混合布局在深色主题下的特殊处理
html.dark {
  .layout-top,
  .layout-mix {
    .logo {
      .logo-icon {
        img {
          filter: brightness(0) invert(1);
        }
      }
    }
  }
}

// 顶部布局和混合布局在深蓝色侧边栏主题下的特殊处理
html.sidebar-color-blue {
  .layout-top,
  .layout-mix {
    .logo {
      .logo-icon {
        img {
          filter: brightness(0) invert(1);
        }
      }
    }
  }
}

// 深色主题特殊处理
html.dark {
  .logo {
    .logo-icon {
      img {
        // 将深色图标转换为白色
        filter: brightness(0) invert(1);
      }
    }

    .title {
      color: var(--sidebar-logo-text-color);
    }
  }
}

// 深蓝色侧边栏主题特殊处理
html.sidebar-color-blue {
  .logo {
    .logo-icon {
      img {
        // 将深色图标转换为白色
        filter: brightness(0) invert(1);
      }
    }

    .title {
      color: var(--sidebar-logo-text-color);
    }
  }
}

// 宽屏时：openSidebar 状态下显示完整Logo+文字
.openSidebar {
  &.layout-top .layout__header-left .logo,
  &.layout-mix .layout__header-logo .logo {
    width: $sidebar-width; // 210px，显示logo+文字
  }
}

// 窄屏时：hideSidebar 状态下只显示Logo图标
.hideSidebar {
  &.layout-top .layout__header-left .logo,
  &.layout-mix .layout__header-logo .logo {
    width: $sidebar-width-collapsed; // 54px，只显示logo
  }

  // 隐藏文字，只显示图标
  .logo .title {
    display: none;
  }
}
</style>
